# Updated Nginx Configuration for DHL Sanitation App with Let's Encrypt SSL
# This configuration supports HTTPS with real SSL certificates

# Main server block for HTTPS
server {
    server_name dot1hundred.com;

    # Frontend routes (DHL Login Service)
    location / {
        # Proxy to HTTPS backend with Let's Encrypt certificates
        proxy_pass https://localhost:3443;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # SSL verification settings for backend communication
        proxy_ssl_verify on;   # Enable SSL verification for Let's Encrypt certificates
        proxy_ssl_session_reuse on;
        proxy_ssl_trusted_certificate /etc/letsencrypt/live/dot1hundred.com/chain.pem;
    }

    # Backend API routes
    location /api/ {
        # Proxy to HTTPS backend API
        proxy_pass https://localhost:3444/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # SSL verification settings for backend communication
        proxy_ssl_verify on;   # Enable SSL verification for Let's Encrypt certificates
        proxy_ssl_session_reuse on;
        proxy_ssl_trusted_certificate /etc/letsencrypt/live/dot1hundred.com/chain.pem;
        
        # CORS headers (if needed)
        add_header 'Access-Control-Allow-Origin' 'https://dot1hundred.com' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;
    }

    # Backend form submission and validation routes
    location ~ ^/(submit-form|validate|view-checklist) {
        # Proxy to HTTPS backend API
        proxy_pass https://localhost:3444;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # SSL verification settings
        proxy_ssl_verify on;   # Enable SSL verification for Let's Encrypt certificates
        proxy_ssl_session_reuse on;
        proxy_ssl_trusted_certificate /etc/letsencrypt/live/dot1hundred.com/chain.pem;
    }

    # Optional: Serve static files directly from nginx for better performance
    location /Public/ {
        root /var/www/sanitation-app;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # SSL configuration (managed by Certbot)
    listen 443 ssl http2;
    ssl_certificate /etc/letsencrypt/live/dot1hundred.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/dot1hundred.com/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
}

# HTTP to HTTPS redirect
server {
    listen 80;
    server_name dot1hundred.com;
    
    # Redirect all HTTP traffic to HTTPS
    return 301 https://$host$request_uri;
}
