server {
    server_name dot1hundred.com;

    # Frontend routes (DHL Login Service)
    location / {
        proxy_pass https://localhost:3443;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # SSL verification settings
        proxy_ssl_verify on;
        proxy_ssl_trusted_certificate /etc/letsencrypt/live/dot1hundred.com/chain.pem;
        proxy_ssl_session_reuse on;
    }

    # Backend API routes
    location ~ ^/(submit-form|validate|view-checklist) {
        proxy_pass https://localhost:3445;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # SSL verification settings
        proxy_ssl_verify on;
        proxy_ssl_trusted_certificate /etc/letsencrypt/live/dot1hundred.com/chain.pem;
        proxy_ssl_session_reuse on;
    }

    # API routes
    location /api/ {
        proxy_pass https://localhost:3444/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # SSL verification settings
        proxy_ssl_verify on;
        proxy_ssl_trusted_certificate /etc/letsencrypt/live/dot1hundred.com/chain.pem;
        proxy_ssl_session_reuse on;
    }

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";

    listen 443 ssl;
    ssl_certificate /etc/letsencrypt/live/dot1hundred.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/dot1hundred.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384';
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
}

server {
    listen 80;
    server_name dot1hundred.com;
    return 301 https://$host$request_uri;
}
