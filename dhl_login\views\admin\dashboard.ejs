<%- contentFor('head') %>
<style>
  .admin-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }

  .dashboard-header {
    background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .dashboard-header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 300;
  }

  .dashboard-header p {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    font-size: 1.1rem;
  }

  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .dashboard-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .card-icon {
    font-size: 2rem;
    margin-right: 1rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #f5f5f5;
  }

  .card-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin: 0;
  }

  .card-description {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }

  .card-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
  }

  .btn-primary {
    background: #d32f2f;
    color: white;
  }

  .btn-primary:hover {
    background: #b71c1c;
    transform: translateY(-1px);
  }

  .btn-secondary {
    background: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
  }

  .btn-secondary:hover {
    background: #e0e0e0;
    transform: translateY(-1px);
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #d32f2f;
  }

  .stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #d32f2f;
    margin-bottom: 0.5rem;
  }

  .stat-label {
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .welcome-section {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    border-left: 4px solid #ffcc00;
  }

  .welcome-section h2 {
    color: #333;
    margin-bottom: 0.5rem;
  }

  .welcome-section p {
    color: #666;
    margin: 0;
  }

  @media (max-width: 768px) {
    .admin-dashboard {
      padding: 1rem;
    }

    .dashboard-header {
      padding: 1.5rem;
    }

    .dashboard-header h1 {
      font-size: 2rem;
    }

    .dashboard-grid {
      grid-template-columns: 1fr;
    }

    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
</style>

<div class="admin-dashboard">
  <div class="dashboard-header">
    <h1>Admin Dashboard</h1>
    <p>Welcome back, <%= user ? user.firstName + ' ' + user.lastName : 'Administrator' %>!</p>
  </div>

  <div class="welcome-section">
    <h2>🎯 Quick Overview</h2>
    <p>Manage users, monitor system activity, and oversee sanitation checklist operations from this central dashboard.</p>
  </div>

  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-number">--</div>
      <div class="stat-label">Total Users</div>
    </div>
    <div class="stat-card">
      <div class="stat-number">--</div>
      <div class="stat-label">Active Sessions</div>
    </div>
    <div class="stat-card">
      <div class="stat-number">--</div>
      <div class="stat-label">Checklists Today</div>
    </div>
    <div class="stat-card">
      <div class="stat-number">--</div>
      <div class="stat-label">System Status</div>
    </div>
  </div>

  <div class="dashboard-grid">
    <div class="dashboard-card">
      <div class="card-header">
        <div class="card-icon">👥</div>
        <h3 class="card-title">User Management</h3>
      </div>
      <p class="card-description">
        Create new user accounts, manage existing users, and configure user permissions for the sanitation checklist system.
      </p>
      <div class="card-actions">
        <a href="/admin/users/new" class="btn btn-primary">
          ➕ Create New User
        </a>
        <a href="/admin/users" class="btn btn-secondary">
          📋 View All Users
        </a>
      </div>
    </div>

    <div class="dashboard-card">
      <div class="card-header">
        <div class="card-icon">📊</div>
        <h3 class="card-title">System Reports</h3>
      </div>
      <p class="card-description">
        View system usage reports, checklist completion statistics, and user activity logs.
      </p>
      <div class="card-actions">
        <a href="/admin/reports" class="btn btn-primary">
          📈 View Reports
        </a>
        <a href="/admin/logs" class="btn btn-secondary">
          📝 System Logs
        </a>
      </div>
    </div>

    <div class="dashboard-card">
      <div class="card-header">
        <div class="card-icon">⚙️</div>
        <h3 class="card-title">System Settings</h3>
      </div>
      <p class="card-description">
        Configure system-wide settings, email notifications, and application preferences.
      </p>
      <div class="card-actions">
        <a href="/admin/settings" class="btn btn-primary">
          🔧 Settings
        </a>
        <a href="/admin/backup" class="btn btn-secondary">
          💾 Backup
        </a>
      </div>
    </div>

    <div class="dashboard-card">
      <div class="card-header">
        <div class="card-icon">🏠</div>
        <h3 class="card-title">Application Access</h3>
      </div>
      <p class="card-description">
        Quick access to the main sanitation checklist application and user dashboard.
      </p>
      <div class="card-actions">
        <a href="/app" class="btn btn-primary">
          🚀 Launch App
        </a>
        <a href="/dashboard" class="btn btn-secondary">
          📱 User Dashboard
        </a>
      </div>
    </div>
  </div>
</div>
