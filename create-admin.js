// Save this as create-admin.js in your project root

const bcrypt = require('bcryptjs');
const { Sequelize } = require('sequelize');
const path = require('path');
const fs = require('fs');

// Make sure the data directory exists
const dataDir = path.join(__dirname, 'dhl_login', 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
  console.log(`Created data directory: ${dataDir}`);
}

// Database connection
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: path.join(dataDir, 'database.sqlite'),
  logging: console.log
});

// Define User model inline to avoid import issues
const User = sequelize.define('User', {
  id: {
    type: Sequelize.UUID,
    defaultValue: Sequelize.UUIDV4,
    primaryKey: true,
    allowNull: false,
  },
  username: {
    type: Sequelize.STRING,
    allowNull: false,
    unique: true
  },
  firstName: {
    type: Sequelize.STRING,
    allowNull: false,
  },
  lastName: {
    type: Sequelize.STRING,
    allowNull: false,
  },
  passwordHash: {
    type: Sequelize.STRING,
    allowNull: false,
  },
  securityQuestion1Id: {
    type: Sequelize.INTEGER,
    allowNull: false,
  },
  securityAnswer1Hash: {
    type: Sequelize.STRING,
    allowNull: false,
  },
  securityQuestion2Id: {
    type: Sequelize.INTEGER,
    allowNull: false,
  },
  securityAnswer2Hash: {
    type: Sequelize.STRING,
    allowNull: false,
  },
  isAdmin: {
    type: Sequelize.BOOLEAN,
    defaultValue: false,
  },
  passwordResetAttemptCount: {
    type: Sequelize.INTEGER,
    defaultValue: 0,
  },
  lastPasswordResetAttempt: {
    type: Sequelize.DATE,
    allowNull: true,
  }
});

async function createAdminUser() {
  try {
    // Test connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');
    
    // Sync the model with the database
    await User.sync();
    console.log('User table synchronized.');
    
    // Check if admin user already exists
    const existingAdmin = await User.findOne({ where: { username: 'admin' } });
    if (existingAdmin) {
      console.log('Admin user already exists.');
      return;
    }
    
    // Create admin user
    const hashedPassword = await bcrypt.hash('password123', 10);
    const securityAnswer1Hash = await bcrypt.hash('answer1', 10);
    const securityAnswer2Hash = await bcrypt.hash('answer2', 10);
    
    await User.create({
      username: 'admin',
      firstName: 'Admin',
      lastName: 'User',
      passwordHash: hashedPassword,
      securityQuestion1Id: 1,
      securityAnswer1Hash: securityAnswer1Hash,
      securityQuestion2Id: 2,
      securityAnswer2Hash: securityAnswer2Hash,
      isAdmin: true
    });
    
    console.log('Admin user created successfully.');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await sequelize.close();
  }
}

createAdminUser();
