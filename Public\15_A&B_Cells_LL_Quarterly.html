<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>15-A & B Cells LL Quarterly</title>
    <link rel="stylesheet" href="dhl-unified.css">
    <script src="config.js"></script>
    <script defer src="scripts.js"></script>
</head>
<body>
    <input type="text" id="scannerInput" style="position: absolute; left: -9999px; top: -9999px;">
    <div class="App">
        <header class="header">
            <img src="dhl-logo.svg" alt="DHL Logo" class="logo">
        </header>

            <h1>Warehouse Sanitation Checklist</h1>

        <div class="header-inputs">
            <label for="name">Associate Name:</label>
            <input type="text" id="name" name="name" placeholder="Enter your name" required>
            <label for="date">Date:</label>
            <input type="date" id="date" name="date" required>
        </div>
            <section>
                <h2 class="no-wrap-heading">Checklist #15-A & B Cells LL Quarterly.html</h2>
            </section>
                <h3>Scan barcode to check boxes as sanitation tasks are completed. If unable to complete, leave blank and note reason in comments.</h3>
        
        <div class="task-container">
            <section>          
            <div class="task1-inputs">
                <h3>Sweep Around & Empty Boots in All A-level Pallet Locations-A Cell</h3>
                <div class="section" id="section1">
                    <div class="grid">
                        <div><input type="checkbox" id="A03"><label for="A03"> A03</label></div>
                        <div><input type="checkbox" id="A08"><label for="A08"> A08</label></div>                        
                        <div><input type="checkbox" id="A13"><label for="A13"> A13</label></div>
                        <div><input type="checkbox" id="A04"><label for="A04"> A04</label></div>
                        <div><input type="checkbox" id="A09"><label for="A09"> A09</label></div>                        
                        <div><input type="checkbox" id="A14"><label for="A14"> A14</label></div>
                        <div><input type="checkbox" id="A05"><label for="A05"> A05</label></div>
                        <div><input type="checkbox" id="A10"><label for="A10"> A10</label></div>
                        <div><input type="checkbox" id="A15"><label for="A15"> A15</label></div>                        
                        <div><input type="checkbox" id="A06"><label for="A06"> A06</label></div>
                        <div><input type="checkbox" id="A11"><label for="A11"> A11</label></div>
                        <div><input type="checkbox" id="A16"><label for="A16"> A16</label></div>
                        <div><input type="checkbox" id="A07"><label for="A07"> A07</label></div>                        
                        <div><input type="checkbox" id="A12"><label for="A12"> A12</label></div>
                        <div><input type="checkbox" id="A17"><label for="A17"> A17</label></div>
                        <div></div>
                        <div></div>
                        <div><input type="checkbox" id="A18"><label for="A18"> A18</label></div>
                        
                    </div>
                </div>
            </div>
            </section>

            <section>          
            <div class="task2-inputs">
                <h3>Sweep Around & Empty Boots in All A-level Pallet Locations-B Cell</h3>
                <div class="grid">
                    <div><input type="checkbox" id="B03"><label for="B03"> B03</label></div>
                    <div><input type="checkbox" id="B18"><label for="B18"> B18</label></div>                        
                    <div><input type="checkbox" id="B21"><label for="B21"> B21</label></div>
                    <div><input type="checkbox" id="B04"><label for="B04"> B04</label></div>
                    <div><input type="checkbox" id="B19"><label for="B19"> B19</label></div>                        
                    <div><input type="checkbox" id="B22"><label for="B22"> B22</label></div>
                    <div><input type="checkbox" id="B05"><label for="B05"> B05</label></div>
                    <div><input type="checkbox" id="B20"><label for="B20"> B10</label></div>
                    <div><input type="checkbox" id="B23"><label for="B23"> B15</label></div>                        
                    <div><input type="checkbox" id="B06"><label for="A06"> A06</label></div>
                    <div></div>
                    <div><input type="checkbox" id="B24"><label for="B24"> B24</label></div>
                </div>
            </div>
            </section>
            
            <section>          
            <div class="task3-inputs">
                <h3>Scrub - C Cell</h3>
                <div class="grid">
                    <div><input type="checkbox" id="West Aisle 14C"><label for="West Aisle 14C"> West Aisle</label></div>
                    <div><input type="checkbox" id="Highway 14C"><label for="MSO West-Mid Aisle 14C"> MSO West-Mid Aisle</label></div>                        
                    <div><input type="checkbox" id="East Aisle 14C"><label for="East Aisle 14C"> East Aisle</label></div>
                    <div><input type="checkbox" id="east Docks 14C"><label for="east Docks 14C"> East Docks</label></div>
                    <div><input type="checkbox" id="West Docks 14C"><label for="West Docks 14C"> West Docks</label></div>
                    <div><input type="checkbox" id="Damage Cage 14C"><label for="Damage Cage 14C"> Damage Cage</label></div>                    
                </div>
            </div>
            </section>
            
            <section>          
            <div class="task4-inputs">
                <h3>Scrub - D Cell</h3>
                <div class="grid">
                    <div><input type="checkbox" id="westAisle 14D"><label for="westAisle 14D"> West Aisle</label></div>
                    <div><input type="checkbox" id="middleAisle 14D"><label for="middleAisle 14D"> MSO West-Mid Aisle</label></div>                        
                    <div><input type="checkbox" id="eastAisle 14D"><label for="East Aisle 14D"> East Aisle</label></div>
                    <div><input type="checkbox" id="eastDocks 14D"><label for="east Docks 14D"> East Docks</label></div>
                    <div><input type="checkbox" id="westDocks 14D"><label for="westDocks 14D"> West Docks</label></div>
                </div>
            </div>
            </section>
            
            <section>          
            <div class="task5-inputs">
                <h3>Scrub - E Cell</h3>
                <div class="grid">
                    <div><input type="checkbox" id="westAisle 14E"><label for="westAisle 14E"> West Aisle</label></div>
                    <div><input type="checkbox" id="westDocks 14E"><label for="westDocks 14E"> West Docks</label></div>                        
                    <div><input type="checkbox" id="eastAisle 14E"><label for="East Aisle 14E"> East Aisle</label></div>
                    <div><input type="checkbox" id="eastDocks 14E"><label for="east Docks 14DE"> East Docks</label></div>
                </div>
            </div>
            </section>
            
            <section>          
            <div class="task6-inputs">
                <h3>Scrub - F Cell</h3>
                <div class="grid">
                    <div><input type="checkbox" id="westAisle 14F"><label for="westAisle 14F"> West Aisle</label></div>
                    <div><input type="checkbox" id="westDocks 14F"><label for="westDocks 14F"> West Docks</label></div>                        
                    <div><input type="checkbox" id="eastAisle 14F"><label for="East Aisle 14F"> East Aisle</label></div>
                    <div><input type="checkbox" id="eastDocks 14F"><label for="east Docks 14DF"> East Docks</label></div>
                    <div><input type="checkbox" id="FC Room 14F"><label for="FC Room 14F"> FC Room</label></div>
                    <div><input type="checkbox" id="FF Room 14F"><label for="FF Room 14F"> FF Room</label></div>                        
                </div>
            </div>
            </section>
            
            <section>          
            <div class="task2-inputs">
                <h3>Wet Mop Sanitation Lines - FC Room</h3>
                <div class="grid">
                    <div><input type="checkbox" id="West Wall 13FC"><label for="West Wall 13FC"> West Wall</label></div>
                    <div><input type="checkbox" id="North Wall 13FC"><label for="North Wall 13FC"> North Wall</label></div>                        
                    <div><input type="checkbox" id="East Wall 13FC"><label for="East Wall 13FC"> East Wall</label></div>
                    <div><input type="checkbox" id="South Wall 13FC"><label for="South Wall 13FC"> South Wall</label></div>
                </div>
            </div>
            </section>

            <section>          
            <div class="task2-inputs">
                <h3>Wet Mop Sanitation Lines - FF Room</h3>
                <div class="grid">
                    <div><input type="checkbox" id="West Wall 13FF"><label for="West Wall 13FF"> West Wall</label></div>
                    <div><input type="checkbox" id="North Wall 13FF"><label for="North Wall 13FF"> North Wall</label></div>                        
                    <div><input type="checkbox" id="East Wall 13FF"><label for="East Wall 13FF"> East Wall</label></div>
                    <div><input type="checkbox" id="South Wall 13FF"><label for="South Wall 13FF"> South Wall</label></div>
                </div>
            </div>
            </section>
                                        
            <div class="comments">
                <label for="comments">Comments:</label>
                <textarea id="comments" name="comments" rows="5" cols="133" placeholder="Enter comments here..."></textarea>
            </div>



            <div class="button">
                <input type="submit" value="Submit">
            </div>
        <div class="footer" style="text-align: center; margin-top: 2rem; font-size: 0.8rem; color: #666;">
            &copy; 2025 DHL Supply Chain | Warehouse Sanitation Checklists
        </div>
    </div>
</body>
</html>

